t 0.060782 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=0) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.163171 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=1) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.265571 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=2) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.367971 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=3) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.470371 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=4) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.572771 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=5) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.675171 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=6) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.777571 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=7) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.879971 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=8) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.982371 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=9) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.08477 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=10) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.18717 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=11) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.28957 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=12) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.39197 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=13) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.49437 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=14) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.59677 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=15) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.69917 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=16) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.80157 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=17) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.90397 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=18) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.00637 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=19) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.10877 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=20) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.21117 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=21) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.31357 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=22) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.41597 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=23) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.51837 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=24) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.62077 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=25) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.72317 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=26) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.82557 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=27) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.92797 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=28) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.03037 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=29) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.13277 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=30) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.23517 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=31) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.33757 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=32) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.43997 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=33) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.54237 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=34) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.64477 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=35) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.74717 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=36) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.84957 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=37) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.95197 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=38) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.05437 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=39) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.15677 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=40) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.25917 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=41) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.36157 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=42) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.46397 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=43) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.56637 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=44) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.66877 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=45) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.77117 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=46) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.87357 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=47) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.97597 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=48) ns3::MgtProbeResponseHeader (ssid=UnderwaterRelay , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
