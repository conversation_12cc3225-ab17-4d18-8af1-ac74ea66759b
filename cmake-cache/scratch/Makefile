# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ns3/ns-allinone-3.40/ns-3.40

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/CMakeFiles /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/scratch//CMakeFiles/progress.marks
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(CMAKE_COMMAND) -P /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/CMakeFiles/VerifyGlobs.cmake
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
scratch/CMakeFiles/scratch_final-underwater-relay.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_final-underwater-relay.dir/rule
.PHONY : scratch/CMakeFiles/scratch_final-underwater-relay.dir/rule

# Convenience name for target.
scratch_final-underwater-relay: scratch/CMakeFiles/scratch_final-underwater-relay.dir/rule
.PHONY : scratch_final-underwater-relay

# fast build rule for target.
scratch_final-underwater-relay/fast:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_final-underwater-relay.dir/build.make scratch/CMakeFiles/scratch_final-underwater-relay.dir/build
.PHONY : scratch_final-underwater-relay/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/rule
.PHONY : scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/rule

# Convenience name for target.
scratch_hybrid-underwater-surface: scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/rule
.PHONY : scratch_hybrid-underwater-surface

# fast build rule for target.
scratch_hybrid-underwater-surface/fast:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/build.make scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/build
.PHONY : scratch_hybrid-underwater-surface/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_scratch-simulator.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_scratch-simulator.dir/rule
.PHONY : scratch/CMakeFiles/scratch_scratch-simulator.dir/rule

# Convenience name for target.
scratch_scratch-simulator: scratch/CMakeFiles/scratch_scratch-simulator.dir/rule
.PHONY : scratch_scratch-simulator

# fast build rule for target.
scratch_scratch-simulator/fast:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_scratch-simulator.dir/build.make scratch/CMakeFiles/scratch_scratch-simulator.dir/build
.PHONY : scratch_scratch-simulator/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_underwater-relay-simple.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_underwater-relay-simple.dir/rule
.PHONY : scratch/CMakeFiles/scratch_underwater-relay-simple.dir/rule

# Convenience name for target.
scratch_underwater-relay-simple: scratch/CMakeFiles/scratch_underwater-relay-simple.dir/rule
.PHONY : scratch_underwater-relay-simple

# fast build rule for target.
scratch_underwater-relay-simple/fast:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simple.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simple.dir/build
.PHONY : scratch_underwater-relay-simple/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/rule
.PHONY : scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/rule

# Convenience name for target.
scratch_underwater-relay-simulation: scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/rule
.PHONY : scratch_underwater-relay-simulation

# fast build rule for target.
scratch_underwater-relay-simulation/fast:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build
.PHONY : scratch_underwater-relay-simulation/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_underwater-surface-relay.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_underwater-surface-relay.dir/rule
.PHONY : scratch/CMakeFiles/scratch_underwater-surface-relay.dir/rule

# Convenience name for target.
scratch_underwater-surface-relay: scratch/CMakeFiles/scratch_underwater-surface-relay.dir/rule
.PHONY : scratch_underwater-surface-relay

# fast build rule for target.
scratch_underwater-surface-relay/fast:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-surface-relay.dir/build.make scratch/CMakeFiles/scratch_underwater-surface-relay.dir/build
.PHONY : scratch_underwater-surface-relay/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/rule
.PHONY : scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/rule

# Convenience name for target.
scratch_subdir_scratch-subdir: scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/rule
.PHONY : scratch_subdir_scratch-subdir

# fast build rule for target.
scratch_subdir_scratch-subdir/fast:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build
.PHONY : scratch_subdir_scratch-subdir/fast

final-underwater-relay.o: final-underwater-relay.cc.o
.PHONY : final-underwater-relay.o

# target to build an object file
final-underwater-relay.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_final-underwater-relay.dir/build.make scratch/CMakeFiles/scratch_final-underwater-relay.dir/final-underwater-relay.cc.o
.PHONY : final-underwater-relay.cc.o

final-underwater-relay.i: final-underwater-relay.cc.i
.PHONY : final-underwater-relay.i

# target to preprocess a source file
final-underwater-relay.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_final-underwater-relay.dir/build.make scratch/CMakeFiles/scratch_final-underwater-relay.dir/final-underwater-relay.cc.i
.PHONY : final-underwater-relay.cc.i

final-underwater-relay.s: final-underwater-relay.cc.s
.PHONY : final-underwater-relay.s

# target to generate assembly for a file
final-underwater-relay.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_final-underwater-relay.dir/build.make scratch/CMakeFiles/scratch_final-underwater-relay.dir/final-underwater-relay.cc.s
.PHONY : final-underwater-relay.cc.s

hybrid-underwater-surface.o: hybrid-underwater-surface.cc.o
.PHONY : hybrid-underwater-surface.o

# target to build an object file
hybrid-underwater-surface.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/build.make scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/hybrid-underwater-surface.cc.o
.PHONY : hybrid-underwater-surface.cc.o

hybrid-underwater-surface.i: hybrid-underwater-surface.cc.i
.PHONY : hybrid-underwater-surface.i

# target to preprocess a source file
hybrid-underwater-surface.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/build.make scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/hybrid-underwater-surface.cc.i
.PHONY : hybrid-underwater-surface.cc.i

hybrid-underwater-surface.s: hybrid-underwater-surface.cc.s
.PHONY : hybrid-underwater-surface.s

# target to generate assembly for a file
hybrid-underwater-surface.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/build.make scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/hybrid-underwater-surface.cc.s
.PHONY : hybrid-underwater-surface.cc.s

scratch-simulator.o: scratch-simulator.cc.o
.PHONY : scratch-simulator.o

# target to build an object file
scratch-simulator.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_scratch-simulator.dir/build.make scratch/CMakeFiles/scratch_scratch-simulator.dir/scratch-simulator.cc.o
.PHONY : scratch-simulator.cc.o

scratch-simulator.i: scratch-simulator.cc.i
.PHONY : scratch-simulator.i

# target to preprocess a source file
scratch-simulator.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_scratch-simulator.dir/build.make scratch/CMakeFiles/scratch_scratch-simulator.dir/scratch-simulator.cc.i
.PHONY : scratch-simulator.cc.i

scratch-simulator.s: scratch-simulator.cc.s
.PHONY : scratch-simulator.s

# target to generate assembly for a file
scratch-simulator.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_scratch-simulator.dir/build.make scratch/CMakeFiles/scratch_scratch-simulator.dir/scratch-simulator.cc.s
.PHONY : scratch-simulator.cc.s

subdir/scratch-subdir-additional-header.o: subdir/scratch-subdir-additional-header.cc.o
.PHONY : subdir/scratch-subdir-additional-header.o

# target to build an object file
subdir/scratch-subdir-additional-header.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir-additional-header.cc.o
.PHONY : subdir/scratch-subdir-additional-header.cc.o

subdir/scratch-subdir-additional-header.i: subdir/scratch-subdir-additional-header.cc.i
.PHONY : subdir/scratch-subdir-additional-header.i

# target to preprocess a source file
subdir/scratch-subdir-additional-header.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir-additional-header.cc.i
.PHONY : subdir/scratch-subdir-additional-header.cc.i

subdir/scratch-subdir-additional-header.s: subdir/scratch-subdir-additional-header.cc.s
.PHONY : subdir/scratch-subdir-additional-header.s

# target to generate assembly for a file
subdir/scratch-subdir-additional-header.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir-additional-header.cc.s
.PHONY : subdir/scratch-subdir-additional-header.cc.s

subdir/scratch-subdir.o: subdir/scratch-subdir.cc.o
.PHONY : subdir/scratch-subdir.o

# target to build an object file
subdir/scratch-subdir.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir.cc.o
.PHONY : subdir/scratch-subdir.cc.o

subdir/scratch-subdir.i: subdir/scratch-subdir.cc.i
.PHONY : subdir/scratch-subdir.i

# target to preprocess a source file
subdir/scratch-subdir.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir.cc.i
.PHONY : subdir/scratch-subdir.cc.i

subdir/scratch-subdir.s: subdir/scratch-subdir.cc.s
.PHONY : subdir/scratch-subdir.s

# target to generate assembly for a file
subdir/scratch-subdir.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir.cc.s
.PHONY : subdir/scratch-subdir.cc.s

underwater-relay-simple.o: underwater-relay-simple.cc.o
.PHONY : underwater-relay-simple.o

# target to build an object file
underwater-relay-simple.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simple.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simple.dir/underwater-relay-simple.cc.o
.PHONY : underwater-relay-simple.cc.o

underwater-relay-simple.i: underwater-relay-simple.cc.i
.PHONY : underwater-relay-simple.i

# target to preprocess a source file
underwater-relay-simple.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simple.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simple.dir/underwater-relay-simple.cc.i
.PHONY : underwater-relay-simple.cc.i

underwater-relay-simple.s: underwater-relay-simple.cc.s
.PHONY : underwater-relay-simple.s

# target to generate assembly for a file
underwater-relay-simple.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simple.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simple.dir/underwater-relay-simple.cc.s
.PHONY : underwater-relay-simple.cc.s

underwater-relay-simulation.o: underwater-relay-simulation.cc.o
.PHONY : underwater-relay-simulation.o

# target to build an object file
underwater-relay-simulation.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/underwater-relay-simulation.cc.o
.PHONY : underwater-relay-simulation.cc.o

underwater-relay-simulation.i: underwater-relay-simulation.cc.i
.PHONY : underwater-relay-simulation.i

# target to preprocess a source file
underwater-relay-simulation.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/underwater-relay-simulation.cc.i
.PHONY : underwater-relay-simulation.cc.i

underwater-relay-simulation.s: underwater-relay-simulation.cc.s
.PHONY : underwater-relay-simulation.s

# target to generate assembly for a file
underwater-relay-simulation.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/underwater-relay-simulation.cc.s
.PHONY : underwater-relay-simulation.cc.s

underwater-surface-relay.o: underwater-surface-relay.cc.o
.PHONY : underwater-surface-relay.o

# target to build an object file
underwater-surface-relay.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-surface-relay.dir/build.make scratch/CMakeFiles/scratch_underwater-surface-relay.dir/underwater-surface-relay.cc.o
.PHONY : underwater-surface-relay.cc.o

underwater-surface-relay.i: underwater-surface-relay.cc.i
.PHONY : underwater-surface-relay.i

# target to preprocess a source file
underwater-surface-relay.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-surface-relay.dir/build.make scratch/CMakeFiles/scratch_underwater-surface-relay.dir/underwater-surface-relay.cc.i
.PHONY : underwater-surface-relay.cc.i

underwater-surface-relay.s: underwater-surface-relay.cc.s
.PHONY : underwater-surface-relay.s

# target to generate assembly for a file
underwater-surface-relay.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-surface-relay.dir/build.make scratch/CMakeFiles/scratch_underwater-surface-relay.dir/underwater-surface-relay.cc.s
.PHONY : underwater-surface-relay.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... scratch_final-underwater-relay"
	@echo "... scratch_hybrid-underwater-surface"
	@echo "... scratch_scratch-simulator"
	@echo "... scratch_subdir_scratch-subdir"
	@echo "... scratch_underwater-relay-simple"
	@echo "... scratch_underwater-relay-simulation"
	@echo "... scratch_underwater-surface-relay"
	@echo "... final-underwater-relay.o"
	@echo "... final-underwater-relay.i"
	@echo "... final-underwater-relay.s"
	@echo "... hybrid-underwater-surface.o"
	@echo "... hybrid-underwater-surface.i"
	@echo "... hybrid-underwater-surface.s"
	@echo "... scratch-simulator.o"
	@echo "... scratch-simulator.i"
	@echo "... scratch-simulator.s"
	@echo "... subdir/scratch-subdir-additional-header.o"
	@echo "... subdir/scratch-subdir-additional-header.i"
	@echo "... subdir/scratch-subdir-additional-header.s"
	@echo "... subdir/scratch-subdir.o"
	@echo "... subdir/scratch-subdir.i"
	@echo "... subdir/scratch-subdir.s"
	@echo "... underwater-relay-simple.o"
	@echo "... underwater-relay-simple.i"
	@echo "... underwater-relay-simple.s"
	@echo "... underwater-relay-simulation.o"
	@echo "... underwater-relay-simulation.i"
	@echo "... underwater-relay-simulation.s"
	@echo "... underwater-surface-relay.o"
	@echo "... underwater-surface-relay.i"
	@echo "... underwater-surface-relay.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(CMAKE_COMMAND) -P /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/CMakeFiles/VerifyGlobs.cmake
	cd /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

