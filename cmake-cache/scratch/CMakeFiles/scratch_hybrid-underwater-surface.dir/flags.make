# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DHAVE_BOOST -DHAVE_BOOST_UNITS -DHAVE_GSL -DHAVE_LIBXML2 -DHAVE_PACKET_H -DHAVE_SQLITE3 -DNS3_ASSERT_ENABLE -DNS3_BUILD_PROFILE_DEBUG -DNS3_LOG_ENABLE -DPROJECT_SOURCE_PATH=\"/home/<USER>/ns3/ns-allinone-3.40/ns-3.40\" -DRAW_SOCK_CREATOR=\"/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/ns3.40-raw-sock-creator-debug\" -DTAP_DEV_CREATOR=\"/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/ns3.40-tap-device-creator-debug\" -D__LINUX__

CXX_INCLUDES = -I/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/include -I/usr -I/usr/include/freetype2 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/gdk-pixbuf-2.0 -I/usr/include/gtk-3.0 -I/usr/include/cairo -I/usr/include/pango-1.0 -I/usr/include/harfbuzz -I/usr/include/atk-1.0 -I/usr/include/libxml2

CXX_FLAGS = -g -fPIE   -fno-semantic-interposition -fdiagnostics-color=always -Wall -std=c++17

# PCH options: scratch/CMakeFiles/scratch_hybrid-underwater-surface.dir/hybrid-underwater-surface.cc.o_OPTIONS = -Winvalid-pch;-include;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/CMakeFiles/stdlib_pch_exec.dir/cmake_pch.hxx

