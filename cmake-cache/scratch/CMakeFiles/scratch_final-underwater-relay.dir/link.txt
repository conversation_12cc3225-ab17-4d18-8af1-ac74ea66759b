/usr/bin/c++ -g CMakeFiles/scratch_final-underwater-relay.dir/final-underwater-relay.cc.o -o ../../build/scratch/ns3.40-final-underwater-relay-debug   -L/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/lib  -Wl,-rpath,/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/lib -Wl,--no-as-needed ../../build/lib/libns3.40-topology-read-debug.so ../../build/lib/libns3.40-tap-bridge-debug.so ../../build/lib/libns3.40-sixlowpan-debug.so ../../build/lib/libns3.40-olsr-debug.so ../../build/lib/libns3.40-nix-vector-routing-debug.so ../../build/lib/libns3.40-netanim-debug.so ../../build/lib/libns3.40-mesh-debug.so ../../build/lib/libns3.40-lte-debug.so ../../build/lib/libns3.40-lr-wpan-debug.so ../../build/lib/libns3.40-internet-apps-debug.so ../../build/lib/libns3.40-flow-monitor-debug.so ../../build/lib/libns3.40-fd-net-device-debug.so ../../build/lib/libns3.40-dsr-debug.so ../../build/lib/libns3.40-dsdv-debug.so ../../build/lib/libns3.40-csma-layout-debug.so ../../build/lib/libns3.40-csma-debug.so ../../build/lib/libns3.40-config-store-debug.so ../../build/lib/libns3.40-buildings-debug.so ../../build/lib/libns3.40-aqua-sim-ng-debug.so ../../build/lib/libns3.40-applications-debug.so ../../build/lib/libns3.40-aodv-debug.so -Wl,--as-needed ../../build/lib/libns3.40-wimax-debug.so ../../build/lib/libns3.40-uan-debug.so ../../build/lib/libns3.40-point-to-point-layout-debug.so ../../build/lib/libns3.40-virtual-net-device-debug.so /usr/lib/x86_64-linux-gnu/libxml2.so /usr/lib/x86_64-linux-gnu/libfreetype.so /usr/lib/x86_64-linux-gnu/libglib-2.0.so /usr/lib/x86_64-linux-gnu/libgthread-2.0.so /usr/lib/x86_64-linux-gnu/libgobject-2.0.so /usr/lib/x86_64-linux-gnu/libgio-2.0.so /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /usr/lib/x86_64-linux-gnu/libgdk-3.so /usr/lib/x86_64-linux-gnu/libgtk-3.so /usr/lib/x86_64-linux-gnu/libcairo.so /usr/lib/x86_64-linux-gnu/libpango-1.0.so /usr/lib/x86_64-linux-gnu/libatk-1.0.so ../../build/lib/libns3.40-point-to-point-debug.so ../../build/lib/libns3.40-wifi-debug.so ../../build/lib/libns3.40-spectrum-debug.so ../../build/lib/libns3.40-propagation-debug.so ../../build/lib/libns3.40-mobility-debug.so ../../build/lib/libns3.40-energy-debug.so ../../build/lib/libns3.40-antenna-debug.so ../../build/lib/libns3.40-internet-debug.so ../../build/lib/libns3.40-traffic-control-debug.so ../../build/lib/libns3.40-bridge-debug.so ../../build/lib/libns3.40-network-debug.so ../../build/lib/libns3.40-stats-debug.so /usr/lib/x86_64-linux-gnu/libsqlite3.so ../../build/lib/libns3.40-core-debug.so -Wl,--no-as-needed /usr/lib/x86_64-linux-gnu/libgsl.so /usr/lib/x86_64-linux-gnu/libgslcblas.so -Wl,--as-needed 
