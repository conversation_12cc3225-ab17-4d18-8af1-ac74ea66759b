# CMake generated Testfile for 
# Source directory: /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch
# Build directory: /home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/scratch
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(ctest-scratch_final-underwater-relay "ns3.40-final-underwater-relay-debug")
set_tests_properties(ctest-scratch_final-underwater-relay PROPERTIES  WORKING_DIRECTORY "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/" _BACKTRACE_TRIPLES "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1655;add_test;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1730;set_runtime_outputdirectory;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;57;build_exec;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;69;create_scratch;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;0;")
add_test(ctest-scratch_hybrid-underwater-surface "ns3.40-hybrid-underwater-surface-debug")
set_tests_properties(ctest-scratch_hybrid-underwater-surface PROPERTIES  WORKING_DIRECTORY "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/" _BACKTRACE_TRIPLES "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1655;add_test;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1730;set_runtime_outputdirectory;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;57;build_exec;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;69;create_scratch;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;0;")
add_test(ctest-scratch_scratch-simulator "ns3.40-scratch-simulator-debug")
set_tests_properties(ctest-scratch_scratch-simulator PROPERTIES  WORKING_DIRECTORY "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/" _BACKTRACE_TRIPLES "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1655;add_test;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1730;set_runtime_outputdirectory;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;57;build_exec;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;69;create_scratch;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;0;")
add_test(ctest-scratch_underwater-relay-simple "ns3.40-underwater-relay-simple-debug")
set_tests_properties(ctest-scratch_underwater-relay-simple PROPERTIES  WORKING_DIRECTORY "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/" _BACKTRACE_TRIPLES "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1655;add_test;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1730;set_runtime_outputdirectory;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;57;build_exec;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;69;create_scratch;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;0;")
add_test(ctest-scratch_underwater-relay-simulation "ns3.40-underwater-relay-simulation-debug")
set_tests_properties(ctest-scratch_underwater-relay-simulation PROPERTIES  WORKING_DIRECTORY "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/" _BACKTRACE_TRIPLES "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1655;add_test;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1730;set_runtime_outputdirectory;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;57;build_exec;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;69;create_scratch;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;0;")
add_test(ctest-scratch_underwater-surface-relay "ns3.40-underwater-surface-relay-debug")
set_tests_properties(ctest-scratch_underwater-surface-relay PROPERTIES  WORKING_DIRECTORY "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/" _BACKTRACE_TRIPLES "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1655;add_test;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1730;set_runtime_outputdirectory;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;57;build_exec;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;69;create_scratch;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;0;")
add_test(ctest-scratch_subdir_scratch-subdir "ns3.40-scratch-subdir-debug")
set_tests_properties(ctest-scratch_subdir_scratch-subdir PROPERTIES  WORKING_DIRECTORY "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/subdir/" _BACKTRACE_TRIPLES "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1655;add_test;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build-support/macros-and-definitions.cmake;1730;set_runtime_outputdirectory;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;57;build_exec;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;99;create_scratch;/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt;0;")
subdirs("nested-subdir")
