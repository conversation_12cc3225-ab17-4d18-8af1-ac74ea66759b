t 0.060782 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=0) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.163171 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=1) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.265571 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=2) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.367971 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=3) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.470371 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=4) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.572771 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=5) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.675171 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=6) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.777571 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=7) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.879971 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=8) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 0.982371 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=9) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.08477 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=10) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.18717 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=11) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.28957 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=12) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.39197 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=13) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.49437 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=14) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.59677 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=15) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.69917 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=16) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.80157 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=17) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 1.90397 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=18) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.00637 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=19) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.10877 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=20) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.21117 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=21) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.31357 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=22) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.41597 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=23) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.51837 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=24) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.62077 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=25) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.72317 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=26) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.82557 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=27) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 2.92797 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=28) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.03037 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=29) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.13277 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=30) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.23517 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=31) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.33757 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=32) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.43997 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=33) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.54237 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=34) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.64477 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=35) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.74717 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=36) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.84957 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=37) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 3.95197 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=38) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.05437 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=39) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.15677 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=40) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.25917 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=41) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.36157 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=42) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.46397 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=43) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.56637 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=44) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.66877 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=45) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.77117 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=46) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.87357 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=47) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 4.97597 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=48) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.07837 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=49) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.18077 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=50) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.28317 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=51) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.38557 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=52) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.48797 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=53) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.59037 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=54) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.69277 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=55) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.79517 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=56) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.89757 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=57) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 5.99997 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=58) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 6.10237 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=59) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 6.20477 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=60) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 6.30717 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=61) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 6.40957 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=62) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 6.51197 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=63) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 6.61437 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=64) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 6.71677 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=65) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 6.81917 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=66) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 6.92157 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=67) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.02397 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=68) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.12637 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=69) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.22877 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=70) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.33117 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=71) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.43357 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=72) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.53597 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=73) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.63837 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=74) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.74077 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=75) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.84317 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=76) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 7.94557 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=77) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.04797 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=78) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.15037 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=79) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.25277 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=80) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.35517 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=81) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.45757 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=82) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.55997 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=83) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.66237 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=84) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.76477 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=85) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.86717 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=86) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 8.96957 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=87) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.07197 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=88) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.17437 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=89) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.27677 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=90) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.37917 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=91) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.48157 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=92) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.58397 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=93) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.68637 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=94) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.78877 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=95) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.89117 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=96) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 9.99357 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=97) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 10.096 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=98) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 10.1984 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=99) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 10.3008 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=100) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 10.4032 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=101) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 10.5056 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=102) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 10.608 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=103) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 10.7104 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=104) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 10.8128 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=105) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 10.9152 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=106) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.0176 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=107) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.12 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=108) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.2224 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=109) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.3248 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=110) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.4272 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=111) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.5296 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=112) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.632 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=113) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.7344 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=114) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.8368 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=115) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 11.9392 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=116) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.0416 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=117) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.144 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=118) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.2464 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=119) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.3488 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=120) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.4512 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=121) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.5536 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=122) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.656 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=123) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.7584 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=124) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.8608 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=125) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 12.9632 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=126) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.0656 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=127) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.168 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=128) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.2704 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=129) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.3728 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=130) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.4752 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=131) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.5776 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=132) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.68 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=133) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.7824 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=134) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.8848 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=135) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 13.9872 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=136) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 14.0896 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=137) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 14.192 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=138) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 14.2944 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=139) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 14.3968 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=140) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 14.4992 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=141) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 14.6016 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=142) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 14.704 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=143) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 14.8064 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=144) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 14.9088 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=145) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.0112 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=146) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.1136 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=147) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.216 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=148) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.3184 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=149) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.4208 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=150) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.5232 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=151) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.6256 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=152) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.728 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=153) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.8304 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=154) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 15.9328 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=155) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.0352 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=156) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.1376 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=157) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.24 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=158) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.3424 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=159) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.4448 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=160) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.5472 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=161) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.6496 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=162) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.752 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=163) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.8544 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=164) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 16.9568 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=165) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.0592 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=166) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.1616 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=167) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.264 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=168) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.3664 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=169) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.4688 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=170) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.5712 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=171) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.6736 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=172) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.776 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=173) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.8784 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=174) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 17.9808 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=175) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 18.0832 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=176) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 18.1856 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=177) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 18.288 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=178) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 18.3904 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=179) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 18.4928 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=180) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 18.5952 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=181) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 18.6976 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=182) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 18.8 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=183) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 18.9024 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=184) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.0048 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=185) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.1072 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=186) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.2096 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=187) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.312 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=188) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.4144 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=189) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.5168 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=190) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.6192 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=191) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.7216 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=192) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.824 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=193) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 19.9264 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=194) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.0288 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=195) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.1312 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=196) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.2336 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=197) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.336 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=198) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.4384 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=199) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.5408 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=200) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.6432 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=201) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.7456 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=202) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.848 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=203) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 20.9504 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=204) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.0528 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=205) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.1552 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=206) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.2576 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=207) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.36 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=208) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.4624 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=209) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.5648 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=210) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.6672 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=211) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.7696 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=212) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.872 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=213) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 21.9744 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=214) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.0768 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=215) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.1792 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=216) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.2816 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=217) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.384 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=218) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.4864 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=219) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.5888 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=220) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.6912 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=221) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.7936 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=222) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.896 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=223) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 22.9984 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=224) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 23.1008 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=225) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 23.2032 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=226) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 23.3056 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=227) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 23.408 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=228) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 23.5104 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=229) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 23.6128 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=230) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 23.7152 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=231) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 23.8176 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=232) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 23.92 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=233) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.0224 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=234) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.1248 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=235) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.2272 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=236) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.3296 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=237) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.432 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=238) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.5344 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=239) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.6368 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=240) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.7392 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=241) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.8416 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=242) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 24.944 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=243) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.0464 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=244) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.1488 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=245) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.2512 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=246) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.3536 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=247) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.456 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=248) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.5584 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=249) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.6608 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=250) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.7632 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=251) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.8656 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=252) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 25.968 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=253) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.0704 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=254) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.1728 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=255) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.2752 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=256) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.3776 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=257) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.48 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=258) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.5824 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=259) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.6848 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=260) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.7872 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=261) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.8896 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=262) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 26.992 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=263) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 27.0944 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=264) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 27.1968 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=265) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 27.2992 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=266) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 27.4016 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=267) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 27.504 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=268) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 27.6064 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=269) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 27.7088 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=270) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 27.8112 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=271) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 27.9136 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=272) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.016 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=273) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.1184 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=274) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.2208 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=275) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.3232 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=276) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.4256 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=277) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.528 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=278) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.6304 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=279) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.7328 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=280) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.8352 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=281) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 28.9376 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=282) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.04 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=283) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.1424 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=284) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.2448 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=285) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.3472 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=286) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.4496 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=287) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.552 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=288) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.6544 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=289) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.7568 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=290) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.8592 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=291) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
t 29.9616 /NodeList/2/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (MGT_BEACON ToDS=0, FromDS=0, MoreFrag=0, Retry=0, PowerManagement=0, MoreData=0 Duration/ID=0us, DA=ff:ff:ff:ff:ff:ff, SA=00:00:00:00:00:01, BSSID=00:00:00:00:00:01, FragNumber=0, SeqNumber=292) ns3::MgtProbeResponseHeader (ssid=RelayNetwork , rates=[*1mbs *2mbs 5mbs 11mbs *6mbs 9mbs *12mbs 18mbs] ,  , 1|0|0 , rates=[*24mbs 36mbs 48mbs 54mbs] , ) 
